// MDAC AI助手 - Chrome扩展实时功能诊断脚本
// 在Chrome扩展side panel控制台中运行此脚本

console.log('🔍 开始MDAC AI助手功能诊断...');

// ===== 1. 基础环境检查 =====
function checkBasicEnvironment() {
    console.log('\n📋 === 基础环境检查 ===');
    
    // 检查Chrome扩展环境
    const chromeEnv = {
        runtime: !!chrome.runtime,
        storage: !!chrome.storage,
        tabs: !!chrome.tabs,
        sidePanel: !!chrome.sidePanel
    };
    console.log('Chrome API可用性:', chromeEnv);
    
    // 检查页面基础元素
    const basicElements = {
        container: !!document.querySelector('.sidepanel-container'),
        header: !!document.querySelector('.header'),
        gridLayout: !!document.querySelector('.grid-layout'),
        formFields: document.querySelectorAll('.field-input').length
    };
    console.log('基础UI元素:', basicElements);
    
    // 检查主要脚本加载
    const scripts = Array.from(document.scripts).map(s => s.src.split('/').pop()).filter(s => s);
    console.log('已加载脚本:', scripts);
    
    return { chromeEnv, basicElements, scripts };
}

// ===== 2. 主控制器状态检查 =====
function checkMainController() {
    console.log('\n🎛️ === 主控制器状态检查 ===');
    
    // 检查MDACMainController
    const controller = window.mdacMainController;
    if (!controller) {
        console.error('❌ MDACMainController未初始化');
        return { status: 'not_initialized' };
    }
    
    console.log('✅ MDACMainController已存在');
    console.log('控制器版本:', controller.version || 'unknown');
    console.log('初始化状态:', controller.initialized);
    
    // 检查配置加载状态
    const configs = {
        aiConfig: controller.aiConfig?.loaded || false,
        cityData: Object.keys(controller.cityData || {}).length,
        fieldMappings: Object.keys(controller.fieldMappings || {}).length
    };
    console.log('配置加载状态:', configs);
    
    // 检查DOM元素缓存
    const elementCache = controller.elements || {};
    const formFieldsCount = Object.keys(elementCache.formFields || {}).length;
    console.log('缓存的表单字段数量:', formFieldsCount);
    
    return { status: 'initialized', configs, formFieldsCount };
}

// ===== 3. 表单字段功能检查 =====
function checkFormFields() {
    console.log('\n📝 === 表单字段功能检查 ===');
    
    const expectedFields = [
        'name', 'passportNo', 'dateOfBirth', 'nationality', 'sex', 'passportExpiry',
        'arrivalDate', 'departureDate', 'flightNo', 'modeOfTravel', 
        'accommodation', 'address', 'state', 'city', 'postcode'
    ];
    
    const fieldStatus = {};
    expectedFields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        fieldStatus[fieldId] = {
            exists: !!element,
            type: element?.type || element?.tagName.toLowerCase(),
            value: element?.value || '',
            disabled: element?.disabled || false
        };
    });
    
    const workingFields = Object.keys(fieldStatus).filter(id => fieldStatus[id].exists);
    console.log(`✅ 有效字段: ${workingFields.length}/${expectedFields.length}`);
    
    // 显示问题字段
    const missingFields = expectedFields.filter(id => !fieldStatus[id].exists);
    if (missingFields.length > 0) {
        console.warn('❌ 缺失字段:', missingFields);
    }
    
    return { fieldStatus, workingFields: workingFields.length, total: expectedFields.length };
}

// ===== 4. AI功能检查 =====
function checkAIFunctionality() {
    console.log('\n🧠 === AI功能检查 ===');
    
    // 检查AI解析按钮
    const aiButtons = {
        parsePersonal: !!document.getElementById('parsePersonalBtn'),
        parseTravel: !!document.getElementById('parseTravelBtn'),
        imageUpload: !!document.getElementById('imageUploadBtn')
    };
    console.log('AI按钮可用性:', aiButtons);
    
    // 检查AI输入区域
    const aiInputs = {
        personalInput: !!document.getElementById('personalInfoInput'),
        travelInput: !!document.getElementById('travelInfoInput'),
        imageInput: !!document.getElementById('imageInput')
    };
    console.log('AI输入区域:', aiInputs);
    
    // 检查自动解析开关
    const autoParseSettings = {
        personalEnabled: document.getElementById('autoParsePersonalEnabled')?.checked,
        travelEnabled: document.getElementById('autoParseTravel_Enabled')?.checked
    };
    console.log('自动解析设置:', autoParseSettings);
    
    return { aiButtons, aiInputs, autoParseSettings };
}

// ===== 5. MDAC网站连接检查 =====
function checkMDACConnection() {
    console.log('\n🌐 === MDAC网站连接检查 ===');
    
    // 检查连接状态指示器
    const connectionStatus = document.getElementById('connectionStatus');
    const currentStatus = connectionStatus?.textContent || 'unknown';
    console.log('当前连接状态:', currentStatus);
    
    // 检查MDAC访问按钮
    const mdacBtn = document.getElementById('mdacAccessBtn');
    console.log('MDAC访问按钮:', !!mdacBtn);
    
    // 检查更新按钮
    const updateBtn = document.getElementById('updateToMDACBtn');
    console.log('更新到MDAC按钮:', !!updateBtn);
    
    return { connectionStatus: currentStatus, hasAccessBtn: !!mdacBtn, hasUpdateBtn: !!updateBtn };
}

// ===== 6. 事件监听器检查 =====
function checkEventListeners() {
    console.log('\n🎯 === 事件监听器检查 ===');
    
    // 测试主要按钮的事件绑定
    const testButtons = [
        'mdacAccessBtn', 'parsePersonalBtn', 'parseTravelBtn', 
        'updateToMDACBtn', 'clearAllBtn', 'cityViewerBtn'
    ];
    
    const buttonEvents = {};
    testButtons.forEach(btnId => {
        const btn = document.getElementById(btnId);
        if (btn) {
            // 检查是否有事件监听器（通过onclick或addEventListener）
            const hasListener = !!btn.onclick || btn.hasAttribute('onclick');
            buttonEvents[btnId] = {
                exists: true,
                hasListener: hasListener,
                disabled: btn.disabled
            };
        } else {
            buttonEvents[btnId] = { exists: false };
        }
    });
    
    console.log('按钮事件绑定状态:', buttonEvents);
    return buttonEvents;
}

// ===== 7. 城市查看器功能检查 =====
function checkCityViewer() {
    console.log('\n🏙️ === 城市查看器功能检查 ===');
    
    const cityViewer = {
        area: !!document.getElementById('cityViewerArea'),
        searchInput: !!document.getElementById('citySearchInput'),
        stateFilter: !!document.getElementById('stateFilter'),
        cityList: !!document.getElementById('cityList'),
        closeBtn: !!document.getElementById('closeViewerBtn')
    };
    
    console.log('城市查看器组件:', cityViewer);
    
    // 检查州属选项
    const stateFilter = document.getElementById('stateFilter');
    const stateOptions = stateFilter ? stateFilter.options.length : 0;
    console.log('可用州属数量:', stateOptions);
    
    return { ...cityViewer, stateOptions };
}

// ===== 8. 数据持久化检查 =====
async function checkDataPersistence() {
    console.log('\n💾 === 数据持久化检查 ===');
    
    try {
        // 尝试读取保存的数据
        const savedData = await chrome.storage.local.get(['mdacFormData', 'mdacPresets']);
        console.log('保存的表单数据存在:', !!savedData.mdacFormData);
        console.log('保存的预设数据存在:', !!savedData.mdacPresets);
        
        // 检查预设字段
        const presetEmail = document.getElementById('presetEmail')?.value;
        const presetPhone = document.getElementById('presetPhone')?.value;
        console.log('当前预设:', { email: presetEmail, phone: presetPhone });
        
        return { savedData, presets: { email: presetEmail, phone: presetPhone } };
    } catch (error) {
        console.error('❌ 数据持久化检查失败:', error);
        return { error: error.message };
    }
}

// ===== 9. 模块化系统检查 =====
function checkModularSystem() {
    console.log('\n🔧 === 模块化系统检查 ===');
    
    // 检查bootstrap系统
    const bootstrap = window.mdacUltimateBootstrap;
    console.log('Ultimate Bootstrap:', !!bootstrap);
    
    if (bootstrap) {
        console.log('Bootstrap统计:', bootstrap.getStats ? bootstrap.getStats() : 'no stats method');
    }
    
    // 检查事件总线
    const eventBus = window.mdacEventBus;
    console.log('事件总线:', !!eventBus);
    
    // 检查模块化侧边栏
    const modularPanel = window.mdacModularSidePanel;
    console.log('模块化侧边栏:', !!modularPanel);
    
    if (modularPanel) {
        console.log('模块状态:', modularPanel.getModuleStatus ? modularPanel.getModuleStatus() : 'no status method');
    }
    
    return { bootstrap: !!bootstrap, eventBus: !!eventBus, modularPanel: !!modularPanel };
}

// ===== 10. 综合诊断函数 =====
async function runCompleteDiagnosis() {
    console.log('🚀 === MDAC AI助手完整诊断开始 ===');
    
    const results = {
        environment: checkBasicEnvironment(),
        controller: checkMainController(),
        formFields: checkFormFields(),
        aiFeatures: checkAIFunctionality(),
        mdacConnection: checkMDACConnection(),
        eventListeners: checkEventListeners(),
        cityViewer: checkCityViewer(),
        dataPersistence: await checkDataPersistence(),
        modularSystem: checkModularSystem()
    };
    
    console.log('\n📊 === 诊断结果汇总 ===');
    
    // 计算健康度评分
    let score = 0;
    let maxScore = 0;
    
    // 基础环境 (20分)
    maxScore += 20;
    if (results.environment.chromeEnv.runtime) score += 5;
    if (results.environment.basicElements.container) score += 5;
    if (results.environment.basicElements.formFields > 10) score += 10;
    
    // 主控制器 (25分)
    maxScore += 25;
    if (results.controller.status === 'initialized') score += 15;
    if (results.controller.formFieldsCount >= 15) score += 10;
    
    // 表单字段 (20分)
    maxScore += 20;
    score += Math.floor((results.formFields.workingFields / results.formFields.total) * 20);
    
    // AI功能 (15分)
    maxScore += 15;
    const aiButtonCount = Object.values(results.aiFeatures.aiButtons).filter(Boolean).length;
    score += aiButtonCount * 5;
    
    // MDAC连接 (10分)
    maxScore += 10;
    if (results.mdacConnection.hasAccessBtn) score += 5;
    if (results.mdacConnection.hasUpdateBtn) score += 5;
    
    // 其他功能 (10分)
    maxScore += 10;
    if (results.cityViewer.area) score += 3;
    if (results.dataPersistence.savedData) score += 3;
    if (results.modularSystem.eventBus) score += 4;
    
    const healthScore = Math.round((score / maxScore) * 100);
    
    console.log(`🎯 整体健康度评分: ${healthScore}% (${score}/${maxScore})`);
    
    // 生成建议
    const recommendations = [];
    if (results.controller.status !== 'initialized') {
        recommendations.push('🔴 优先修复: MDACMainController初始化失败');
    }
    if (results.formFields.workingFields < 15) {
        recommendations.push('🟡 检查: 表单字段映射不完整');
    }
    if (!results.mdacConnection.hasUpdateBtn) {
        recommendations.push('🟡 检查: MDAC连接功能可能异常');
    }
    if (!results.modularSystem.eventBus) {
        recommendations.push('🟡 检查: 模块化系统可能未正确加载');
    }
    
    if (recommendations.length > 0) {
        console.log('\n🔧 修复建议:');
        recommendations.forEach(rec => console.log(rec));
    } else {
        console.log('\n✅ 所有核心功能运行正常！');
    }
    
    console.log('\n📋 完整诊断数据:', results);
    return { healthScore, results, recommendations };
}

// 自动执行诊断
runCompleteDiagnosis().then(diagnosisResult => {
    console.log('\n🎉 诊断完成！');
    console.log('可以通过 window.lastDiagnosisResult 访问详细结果');
    window.lastDiagnosisResult = diagnosisResult;
}).catch(error => {
    console.error('❌ 诊断过程出错:', error);
});
